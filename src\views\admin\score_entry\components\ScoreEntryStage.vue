<template>
  <div class="space-y-6">
    <!-- Filter Summary Bar -->
    <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-maneb-primary">
      <div class="flex items-center justify-between">
        <div>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Selected Criteria</h3>
          <div class="text-sm text-gray-600 space-y-1">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
              <span><strong>Division:</strong> {{ getDivisionName() }}</span>
              <span><strong>District:</strong> {{ getDistrictName() }}</span>
              <span><strong>Center:</strong> {{ getCenterName() }}</span>
              <span><strong>Exam Type:</strong> {{ getExamTypeName() }}</span>
              <span><strong>School:</strong> {{ getSchoolName() }}</span>
              <span><strong>Subject:</strong> {{ getSubjectName() }}</span>
              <span><strong>Paper:</strong> {{ getPaperName() }}</span>
              <span><strong>Score Type:</strong> {{ getScoreTypeName() }}</span>
            </div>
          </div>
        </div>
        <button
          @click="editFilters"
          class="inline-flex items-center px-4 py-2 text-sm font-medium text-maneb-primary bg-white border border-maneb-primary rounded-lg hover:bg-maneb-primary hover:text-white focus:ring-4 focus:outline-none focus:ring-maneb-primary/50 transition-colors duration-200"
        >
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          Edit Filters
        </button>
      </div>
    </div>

    <!-- Exam Number Selection -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium text-gray-900">Select Exam Number</h3>
        <span v-if="selectedExamNumber" class="text-sm text-green-600 font-medium">
          ✓ {{ getSelectedExamNumberName() }}
        </span>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="examNumber" class="block text-sm font-medium text-gray-700 mb-2">
            Exam Number <span class="text-red-500">*</span>
          </label>
          <div class="relative">
            <input
              type="text"
              id="examNumber"
              v-model="examNumberSearch"
              @input="onSearchInput"
              @focus="showDropdown = true"
              @blur="handleBlur"
              @keydown="handleKeydown"
              :placeholder="availableExamNumbers.length === 0 ? 'Loading exam numbers...' : 'Search exam number...'"
              :disabled="availableExamNumbers.length === 0"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 pr-10 disabled:bg-gray-100 disabled:cursor-not-allowed"
              autocomplete="off"
            />
            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
              <button
                v-if="examNumberSearch"
                @click="clearSearch"
                type="button"
                class="mr-2 text-gray-400 hover:text-gray-600 focus:outline-none"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>

            <!-- Dropdown -->
            <div
              v-if="showDropdown && filteredExamNumbers.length > 0"
              class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg dropdown-list"
            >
              <ul class="py-1">
                <li
                  v-for="(examNumber, index) in filteredExamNumbers"
                  :key="examNumber.id"
                  @mousedown="selectExamNumber(examNumber)"
                  @mouseenter="highlightedIndex = index"
                  :class="[
                    'px-3 py-2 cursor-pointer text-sm transition-colors duration-150',
                    index === highlightedIndex ? 'dropdown-item-highlighted' : 'text-gray-900 hover:bg-gray-100'
                  ]"
                >
                  <div class="font-medium">{{ examNumber.name }}</div>
                  <div :class="[
                    'text-xs',
                    index === highlightedIndex ? 'text-white text-opacity-80' : 'text-gray-500'
                  ]">{{ examNumber.subject }} - {{ examNumber.paper }}</div>
                </li>
              </ul>
            </div>

            <!-- No results -->
            <div
              v-if="showDropdown && examNumberSearch && filteredExamNumbers.length === 0"
              class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg"
            >
              <div class="px-3 py-2 text-sm text-gray-500">
                No exam numbers found matching "{{ examNumberSearch }}"
              </div>
            </div>
          </div>
        </div>

        <div v-if="selectedExamNumber" class="flex items-end">
          <button
            @click="loadStudentsForExam"
            :disabled="isLoading"
            class="w-full inline-flex items-center justify-center px-4 py-2.5 text-sm font-medium text-white bg-maneb-primary rounded-lg hover:bg-maneb-primary-dark focus:ring-4 focus:outline-none focus:ring-maneb-primary/50 disabled:bg-gray-300 disabled:cursor-not-allowed"
          >
            <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            {{ isLoading ? 'Loading Students...' : 'Load Students' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Score Entry Table -->
    <div v-if="studentsLoaded" class="bg-white rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <div>
            <h3 class="text-lg font-medium text-gray-900">Score Entry</h3>
            <p class="text-sm text-gray-500 mt-1">Enter scores for eligible students</p>
          </div>
          <div class="flex items-center space-x-3">
            <div class="text-sm text-gray-500">
              <span>{{ filteredStudents.length }} of {{ eligibleStudents.length }} students shown</span>
              <span class="mx-2">•</span>
              <span>{{ completedScores }}/{{ eligibleStudents.length }} scores entered</span>
            </div>
            <button
              @click="saveAllScores"
              :disabled="!hasUnsavedChanges || isSaving"
              class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 focus:ring-4 focus:outline-none focus:ring-green-300 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <svg v-if="isSaving" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
              </svg>
              {{ isSaving ? 'Saving...' : 'Save All Changes' }}
            </button>
          </div>
        </div>

        <!-- Progress Bar -->
        <div v-if="eligibleStudents.length > 0" class="mb-4">
          <div class="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>Score Entry Progress</span>
            <span>{{ Math.round((completedScores / eligibleStudents.length) * 100) }}% Complete</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="bg-maneb-primary h-2 rounded-full transition-all duration-300"
              :style="{ width: `${(completedScores / eligibleStudents.length) * 100}%` }"
            ></div>
          </div>
        </div>

        <!-- Real-time Student Search -->
        <div class="mb-4">
          <div class="flex items-center justify-between mb-3">
            <label for="studentSearch" class="text-sm font-medium text-gray-700">
              Search Students by Exam Number
            </label>
            <button
              v-if="studentSearchQuery"
              @click="clearStudentSearch"
              class="text-sm text-maneb-primary hover:text-maneb-primary-dark focus:outline-none"
            >
              Clear search
            </button>
          </div>
          <div class="relative">
            <input
              type="text"
              id="studentSearch"
              v-model="studentSearchQuery"
              @input="onStudentSearchInput"
              placeholder="Type exam number to filter students..."
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5 pr-10"
              autocomplete="off"
            />
            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
              <button
                v-if="studentSearchQuery"
                @click="clearStudentSearch"
                type="button"
                class="mr-2 text-gray-400 hover:text-gray-600 focus:outline-none"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
          </div>

          <!-- Search Results Info -->
          <div v-if="studentSearchQuery" class="mt-2 text-sm text-gray-600">
            <span v-if="filteredStudents.length > 0">
              Found {{ filteredStudents.length }} student{{ filteredStudents.length !== 1 ? 's' : '' }} matching "{{ studentSearchQuery }}"
            </span>
            <span v-else class="text-orange-600">
              No students found matching "{{ studentSearchQuery }}"
            </span>
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full text-left text-sm text-gray-500">
          <thead class="bg-gray-50 text-xs uppercase text-gray-700">
            <tr>
              <th scope="col" class="px-6 py-3">Student ID</th>
              <th scope="col" class="px-6 py-3">Exam Number</th>
              <th scope="col" class="px-6 py-3">Student Name</th>
              <th scope="col" class="px-6 py-3 hidden md:table-cell">Subject Name</th>
              <th scope="col" class="px-6 py-3">Score</th>
              <th scope="col" class="px-6 py-3">Grade</th>
              <th scope="col" class="px-6 py-3">Actions</th>
            </tr>
          </thead>
          <tbody>
            <!-- Loading State -->
            <tr v-if="isLoading">
              <td colspan="7" class="px-6 py-12 text-center">
                <div class="flex items-center justify-center">
                  <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-maneb-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="text-gray-600">Loading students...</span>
                </div>
              </td>
            </tr>
            
            <!-- No Students Found -->
            <tr v-else-if="filteredStudents.length === 0 && studentSearchQuery">
              <td colspan="7" class="px-6 py-12 text-center">
                <div class="flex flex-col items-center">
                  <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                  <h3 class="text-lg font-medium text-gray-900 mb-2">No students found</h3>
                  <p class="text-gray-500 mb-4">No students match your search for "{{ studentSearchQuery }}"</p>
                  <button
                    @click="clearStudentSearch"
                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-maneb-primary bg-white border border-maneb-primary rounded-lg hover:bg-maneb-primary hover:text-white focus:ring-4 focus:outline-none focus:ring-maneb-primary/50 transition-colors duration-200"
                  >
                    Clear search
                  </button>
                </div>
              </td>
            </tr>

            <!-- Student Rows -->
            <tr v-else v-for="student in filteredStudents" :key="student.id" class="border-b hover:bg-gray-50">
              <td class="px-6 py-4 font-medium text-gray-900">{{ student.studentId }}</td>
              <td class="px-6 py-4 text-black">{{ student.examNumber }}</td>
              <td class="px-6 py-4 text-black">{{ student.fullName }}</td>
              <td class="px-6 py-4 text-black hidden md:table-cell">{{ student.subjectName }}</td>
              <td class="px-6 py-4">
                <div class="flex items-center space-x-2">
                  <input
                    type="number"
                    v-model="student.tempScore"
                    min="0"
                    max="100"
                    step="0.1"
                    placeholder="Enter score"
                    class="w-24 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-maneb-primary focus:border-maneb-primary bg-white"
                    @input="onScoreInput(student)"
                    @keyup.enter="saveScore(student)"
                    @keyup.escape="resetScore(student)"
                    @blur="saveScore(student)"
                    :data-student-id="student.id"
                  />
                  <span v-if="student.hasUnsavedChanges" class="text-orange-500 text-xs" title="Unsaved changes">●</span>
                </div>
              </td>
              <td class="px-6 py-4">
                <span v-if="student.tempScore !== null && student.tempScore >= 0 && student.tempScore <= 100" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" :class="getGradeBadgeClass(student.tempScore)">
                  {{ calculateGrade(student.tempScore) }}
                </span>
                <span v-else class="text-gray-400">-</span>
              </td>
              <td class="px-6 py-4">
                <div class="flex items-center space-x-2">
                  <button
                    v-if="!student.isEditing"
                    @click="startEdit(student)"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded hover:bg-blue-100 focus:ring-2 focus:outline-none focus:ring-blue-300"
                  >
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit
                  </button>
                  
                  <template v-else>
                    <button
                      @click="saveScore(student)"
                      class="inline-flex items-center px-2 py-1 text-xs font-medium text-green-600 bg-green-50 rounded hover:bg-green-100 focus:ring-2 focus:outline-none focus:ring-green-300"
                    >
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Save
                    </button>
                    <button
                      @click="cancelEdit(student)"
                      class="inline-flex items-center px-2 py-1 text-xs font-medium text-red-600 bg-red-50 rounded hover:bg-red-100 focus:ring-2 focus:outline-none focus:ring-red-300"
                    >
                      <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                      </svg>
                      Cancel
                    </button>
                  </template>
                  
                  <button
                    @click="viewStudent(student)"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 bg-gray-50 rounded hover:bg-gray-100 focus:ring-2 focus:outline-none focus:ring-gray-300"
                  >
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    View
                  </button>

                  <button
                    @click="openNotesModal(student)"
                    class="inline-flex items-center px-2 py-1 text-xs font-medium text-maneb-primary bg-red-50 rounded hover:bg-red-100 focus:ring-2 focus:outline-none focus:ring-maneb-primary/50"
                  >
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Notes
                    <span v-if="student.hasNotes" class="ml-1 w-2 h-2 bg-maneb-primary rounded-full"></span>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Empty State -->
        <div v-if="!isLoading && eligibleStudents.length === 0" class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No students found</h3>
          <p class="mt-1 text-sm text-gray-500">No students are registered for the selected criteria.</p>
        </div>
      </div>
    </div>

    <!-- Initial State - No Exam Number Selected -->
    <div v-else class="bg-white rounded-lg shadow-sm p-12 text-center">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <h3 class="mt-2 text-lg font-medium text-gray-900">Select Exam Number</h3>
      <p class="mt-1 text-sm text-gray-500">Choose an exam number above to load eligible students for score entry.</p>
    </div>

    <!-- MANEB Validation Modal -->
    <ManebValidation
      :is-open="isValidationModalOpen"
      :score-data="pendingScoreData"
      @close="closeValidationModal"
      @confirmed="handleValidationConfirmed"
    />

    <!-- Student Notes Modal -->
    <div
      v-if="isNotesModalOpen"
      class="fixed top-0 left-0 right-0 z-50 w-full p-4 overflow-x-hidden overflow-y-auto md:inset-0 h-[calc(100%-1rem)] max-h-full bg-gray-900 bg-opacity-50 flex items-center justify-center"
      @click.self="closeNotesModal"
    >
      <div class="relative w-full max-w-2xl max-h-full">
        <div class="relative bg-white rounded-lg shadow">
          <!-- Modal header -->
          <div class="flex items-start justify-between p-4 border-b rounded-t">
            <h3 class="text-xl font-semibold text-gray-900">
              Student Notes - {{ selectedStudentForNotes?.fullName }}
            </h3>
            <button
              @click="closeNotesModal"
              type="button"
              class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ml-auto inline-flex justify-center items-center"
            >
              <svg class="w-3 h-3" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
              </svg>
            </button>
          </div>

          <!-- Modal body -->
          <div class="p-6 space-y-4">
            <!-- Student Info -->
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-gray-600">Student ID:</span>
                  <span class="ml-2 font-medium text-black">{{ selectedStudentForNotes?.studentId }}</span>
                </div>
                <div>
                  <span class="text-gray-600">Exam Number:</span>
                  <span class="ml-2 font-medium text-black">{{ selectedStudentForNotes?.examNumber }}</span>
                </div>
                <div>
                  <span class="text-gray-600">Subject:</span>
                  <span class="ml-2 font-medium text-black">{{ selectedStudentForNotes?.subjectName }}</span>
                </div>
                <div>
                  <span class="text-gray-600">Current Score:</span>
                  <span class="ml-2 font-medium" :class="getScoreColor(selectedStudentForNotes?.score)">
                    {{ selectedStudentForNotes?.score !== null ? selectedStudentForNotes?.score + '%' : 'Not entered' }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Notes Input -->
            <div>
              <label for="studentNotes" class="block mb-2 text-sm font-medium text-gray-900">
                Notes
              </label>
              <textarea
                id="studentNotes"
                v-model="currentNotes"
                rows="6"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-maneb-primary focus:border-maneb-primary block w-full p-2.5"
                placeholder="Enter notes about this student's performance, special circumstances, or other relevant information..."
              ></textarea>
              <p class="mt-1 text-xs text-gray-500">These notes will be saved with the student's record and can be viewed by authorized personnel.</p>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-end space-x-3 pt-4 border-t">
              <button
                @click="closeNotesModal"
                type="button"
                class="text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900"
              >
                Cancel
              </button>
              <button
                @click="saveNotes"
                type="button"
                :disabled="isSavingNotes"
                class="text-white bg-maneb-primary hover:bg-maneb-primary-dark focus:ring-4 focus:outline-none focus:ring-maneb-primary/50 font-medium rounded-lg text-sm px-5 py-2.5 disabled:bg-gray-300 disabled:cursor-not-allowed"
              >
                <span v-if="isSavingNotes" class="flex items-center">
                  <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </span>
                <span v-else>Save Notes</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, onUnmounted } from 'vue'
import sweetAlert from '@/utils/sweetAlert'
import ManebValidation from './ManebValidation.vue'

// Props
interface Props {
  selectedFilters: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'edit-filters': []
  'score-updated': [data: any]
}>()

// Types
interface Student {
  id: string
  studentId: string
  examNumber: string
  fullName: string
  subjectName: string
  score: number | null
  tempScore: number | null
  hasUnsavedChanges: boolean
  notes: string
  hasNotes: boolean
}

// Reactive state
const eligibleStudents = ref<Student[]>([])
const isLoading = ref(false)
const isSaving = ref(false)
const isValidationModalOpen = ref(false)
const pendingScoreData = ref<any>(null)
const isNotesModalOpen = ref(false)
const selectedStudentForNotes = ref<Student | null>(null)
const currentNotes = ref('')
const isSavingNotes = ref(false)
const selectedExamNumber = ref('')
const availableExamNumbers = ref<any[]>([])
const studentsLoaded = ref(false)
const examNumberSearch = ref('')
const showDropdown = ref(false)
const highlightedIndex = ref(-1)
const selectedExamNumberData = ref<any>(null)
const studentSearchQuery = ref('')
const debouncedSearchQuery = ref('')
const searchTimeout = ref<NodeJS.Timeout | null>(null)

// Mock data
const mockStudents: Student[] = [
  {
    id: '1',
    studentId: 'STU001',
    examNumber: 'MATH001',
    fullName: 'John Banda',
    subjectName: 'Mathematics',
    score: 85,
    tempScore: 85,
    hasUnsavedChanges: false,
    notes: 'Excellent performance in algebra section.',
    hasNotes: true
  },
  {
    id: '2',
    studentId: 'STU002',
    examNumber: 'MATH001',
    fullName: 'Mary Phiri',
    subjectName: 'Mathematics',
    score: null,
    tempScore: null,
    hasUnsavedChanges: false,
    notes: '',
    hasNotes: false
  },
  {
    id: '3',
    studentId: 'STU003',
    examNumber: 'MATH001',
    fullName: 'Peter Mwale',
    subjectName: 'Mathematics',
    score: 72,
    tempScore: 72,
    hasUnsavedChanges: false,
    notes: 'Struggled with geometry questions but showed good understanding of calculus.',
    hasNotes: true
  },
  {
    id: '4',
    studentId: 'STU004',
    examNumber: 'MATH002',
    fullName: 'Grace Tembo',
    subjectName: 'Mathematics',
    score: 91,
    tempScore: 91,
    hasUnsavedChanges: false,
    notes: 'Outstanding performance across all sections.',
    hasNotes: true
  },
  {
    id: '5',
    studentId: 'STU005',
    examNumber: 'MATH003',
    fullName: 'James Zulu',
    subjectName: 'Mathematics',
    score: null,
    tempScore: null,
    hasUnsavedChanges: false,
    notes: '',
    hasNotes: false
  },
  {
    id: '6',
    studentId: 'STU006',
    examNumber: 'ENG001',
    fullName: 'Sarah Banda',
    subjectName: 'English Language',
    score: 78,
    tempScore: 78,
    hasUnsavedChanges: false,
    notes: 'Good essay writing skills.',
    hasNotes: true
  },
  {
    id: '7',
    studentId: 'STU007',
    examNumber: 'ENG002',
    fullName: 'Michael Phiri',
    subjectName: 'English Language',
    score: null,
    tempScore: null,
    hasUnsavedChanges: false,
    notes: '',
    hasNotes: false
  },
  {
    id: '8',
    studentId: 'STU008',
    examNumber: 'SCI001',
    fullName: 'Linda Mwale',
    subjectName: 'Physical Science',
    score: 84,
    tempScore: 84,
    hasUnsavedChanges: false,
    notes: 'Strong in physics, needs improvement in chemistry.',
    hasNotes: true
  }
]

// Computed properties
const hasUnsavedChanges = computed(() => {
  return eligibleStudents.value.some(student => student.hasUnsavedChanges)
})

const completedScores = computed(() => {
  return eligibleStudents.value.filter(student => student.score !== null).length
})

const filteredExamNumbers = computed(() => {
  if (!examNumberSearch.value) {
    return availableExamNumbers.value
  }

  const searchTerm = examNumberSearch.value.toLowerCase()
  return availableExamNumbers.value.filter(exam =>
    exam.name.toLowerCase().includes(searchTerm) ||
    exam.subject.toLowerCase().includes(searchTerm) ||
    exam.paper.toLowerCase().includes(searchTerm)
  )
})

const filteredStudents = computed(() => {
  if (!studentSearchQuery.value.trim()) {
    return eligibleStudents.value
  }

  const searchTerm = studentSearchQuery.value.toLowerCase().trim()
  return eligibleStudents.value.filter(student =>
    student.examNumber.toLowerCase().includes(searchTerm) ||
    student.studentId.toLowerCase().includes(searchTerm) ||
    student.fullName.toLowerCase().includes(searchTerm)
  )
})

// Mock exam numbers data
const mockExamNumbers = [
  { id: '1', name: 'MATH001-2024', subject: 'Mathematics', paper: 'Paper 1 (Pure Mathematics)' },
  { id: '2', name: 'MATH002-2024', subject: 'Mathematics', paper: 'Paper 2 (Applied Mathematics)' },
  { id: '3', name: 'MATH003-2024', subject: 'Mathematics', paper: 'Paper 1 (Pure Mathematics)' },
  { id: '4', name: 'ENG001-2024', subject: 'English Language', paper: 'Paper 1 (Language)' },
  { id: '5', name: 'ENG002-2024', subject: 'English Language', paper: 'Paper 2 (Literature)' },
  { id: '6', name: 'ENG003-2024', subject: 'English Language', paper: 'Paper 1 (Language)' },
  { id: '7', name: 'SCI001-2024', subject: 'Physical Science', paper: 'Paper 1 (Theory)' },
  { id: '8', name: 'SCI002-2024', subject: 'Physical Science', paper: 'Paper 2 (Practical)' },
  { id: '9', name: 'SCI003-2024', subject: 'Physical Science', paper: 'Paper 3 (Alternative to Practical)' },
  { id: '10', name: 'BIO001-2024', subject: 'Biology', paper: 'Paper 1 (Theory)' },
  { id: '11', name: 'BIO002-2024', subject: 'Biology', paper: 'Paper 2 (Practical)' },
  { id: '12', name: 'CHEM001-2024', subject: 'Chemistry', paper: 'Paper 1 (Theory)' },
  { id: '13', name: 'CHEM002-2024', subject: 'Chemistry', paper: 'Paper 2 (Practical)' },
  { id: '14', name: 'PHYS001-2024', subject: 'Physics', paper: 'Paper 1 (Theory)' },
  { id: '15', name: 'PHYS002-2024', subject: 'Physics', paper: 'Paper 2 (Practical)' },
  { id: '16', name: 'GEO001-2024', subject: 'Geography', paper: 'Paper 1 (Physical Geography)' },
  { id: '17', name: 'GEO002-2024', subject: 'Geography', paper: 'Paper 2 (Human Geography)' },
  { id: '18', name: 'HIST001-2024', subject: 'History', paper: 'Paper 1 (World History)' },
  { id: '19', name: 'HIST002-2024', subject: 'History', paper: 'Paper 2 (African History)' },
  { id: '20', name: 'ECON001-2024', subject: 'Economics', paper: 'Paper 1 (Microeconomics)' }
]

// Methods
const loadAvailableExamNumbers = () => {
  // Filter exam numbers based on selected filters
  // In a real implementation, this would be an API call
  availableExamNumbers.value = mockExamNumbers.filter(exam => {
    // Filter based on subject and paper from selected filters
    return exam.subject === props.selectedFilters?.subjectName &&
           exam.paper === props.selectedFilters?.paperName
  })
}

const loadStudentsForExam = async () => {
  if (!selectedExamNumber.value) return

  isLoading.value = true
  try {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    eligibleStudents.value = [...mockStudents]
    studentsLoaded.value = true

    sweetAlert.success('Students Loaded', `Students loaded for exam ${getSelectedExamNumberName()}`)
  } catch (error) {
    console.error('Error loading students:', error)
    sweetAlert.error('Error', 'Failed to load students')
  } finally {
    isLoading.value = false
  }
}

const onSearchInput = () => {
  showDropdown.value = true
  highlightedIndex.value = -1

  // Clear selection if search doesn't match current selection
  if (selectedExamNumberData.value && !selectedExamNumberData.value.name.toLowerCase().includes(examNumberSearch.value.toLowerCase())) {
    selectedExamNumber.value = ''
    selectedExamNumberData.value = null
  }
}

const clearSearch = () => {
  examNumberSearch.value = ''
  selectedExamNumber.value = ''
  selectedExamNumberData.value = null
  showDropdown.value = false
  highlightedIndex.value = -1
  onExamNumberChange()
}

const selectExamNumber = (examNumber: any) => {
  selectedExamNumber.value = examNumber.id
  selectedExamNumberData.value = examNumber
  examNumberSearch.value = examNumber.name
  showDropdown.value = false
  highlightedIndex.value = -1
  onExamNumberChange()
}

const handleBlur = () => {
  // Delay hiding dropdown to allow for click events
  setTimeout(() => {
    showDropdown.value = false
    highlightedIndex.value = -1

    // If no valid selection, clear the search
    if (!selectedExamNumber.value) {
      examNumberSearch.value = ''
    }
  }, 150)
}

const handleKeydown = (event: KeyboardEvent) => {
  if (!showDropdown.value) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      highlightedIndex.value = Math.min(highlightedIndex.value + 1, filteredExamNumbers.value.length - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1)
      break
    case 'Enter':
      event.preventDefault()
      if (highlightedIndex.value >= 0 && filteredExamNumbers.value[highlightedIndex.value]) {
        selectExamNumber(filteredExamNumbers.value[highlightedIndex.value])
      }
      break
    case 'Escape':
      showDropdown.value = false
      highlightedIndex.value = -1
      break
  }
}

const onExamNumberChange = () => {
  studentsLoaded.value = false
  eligibleStudents.value = []
}

// Student search methods
const onStudentSearchInput = () => {
  // Clear any existing timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }

  // Debounce the search to avoid excessive filtering
  searchTimeout.value = setTimeout(() => {
    debouncedSearchQuery.value = studentSearchQuery.value
  }, 300)
}

const clearStudentSearch = () => {
  studentSearchQuery.value = ''
  debouncedSearchQuery.value = ''
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
    searchTimeout.value = null
  }
}

const getSelectedExamNumberName = () => {
  return selectedExamNumberData.value?.name || ''
}

const calculateGrade = (score: number): string => {
  if (score >= 80) return 'A'
  if (score >= 70) return 'B'
  if (score >= 60) return 'C'
  if (score >= 50) return 'D'
  return 'F'
}

const getScoreColor = (score: number | null): string => {
  if (score === null) return 'text-gray-400'
  if (score >= 80) return 'text-green-600'
  if (score >= 70) return 'text-blue-600'
  if (score >= 60) return 'text-yellow-600'
  if (score >= 50) return 'text-orange-600'
  return 'text-red-600'
}

const getGradeBadgeClass = (score: number): string => {
  if (score >= 80) return 'bg-green-100 text-green-800'
  if (score >= 70) return 'bg-blue-100 text-blue-800'
  if (score >= 60) return 'bg-yellow-100 text-yellow-800'
  if (score >= 50) return 'bg-orange-100 text-orange-800'
  return 'bg-red-100 text-red-800'
}

const startEdit = (student: Student) => {
  student.isEditing = true
  student.tempScore = student.score

  // Auto-focus the input field
  setTimeout(() => {
    const input = document.querySelector(`input[data-student-id="${student.id}"]`) as HTMLInputElement
    if (input) {
      input.focus()
      input.select()
    }
  }, 50)
}

const cancelEdit = (student: Student) => {
  student.isEditing = false
  student.tempScore = null
  student.hasUnsavedChanges = false
}

const saveScore = async (student: Student) => {
  if (student.tempScore !== null && student.tempScore >= 0 && student.tempScore <= 100) {
    // Open MANEB validation modal for SOPs compliance
    pendingScoreData.value = {
      studentId: student.studentId,
      examNumber: student.examNumber,
      subject: student.subjectName,
      score: student.tempScore,
      student: student
    }
    isValidationModalOpen.value = true
  } else {
    sweetAlert.error('Invalid Score', 'Please enter a score between 0 and 100')
  }
}

const saveAllScores = async () => {
  isSaving.value = true
  try {
    // Mock API call to save all changes
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Mark all changes as saved
    eligibleStudents.value.forEach(student => {
      student.hasUnsavedChanges = false
    })
    
    sweetAlert.success('Success', 'All scores have been saved successfully')
  } catch (error) {
    console.error('Error saving scores:', error)
    sweetAlert.error('Error', 'Failed to save scores')
  } finally {
    isSaving.value = false
  }
}

const viewStudent = (student: Student) => {
  sweetAlert.info('Student Details', `Viewing details for ${student.fullName}`)
}

const editFilters = () => {
  emit('edit-filters')
}

const closeValidationModal = () => {
  isValidationModalOpen.value = false
  pendingScoreData.value = null
}

const handleValidationConfirmed = (auditData: any) => {
  const student = pendingScoreData.value?.student
  if (student) {
    // Apply the score after MANEB validation
    student.score = pendingScoreData.value.score
    student.hasUnsavedChanges = true
    student.isEditing = false

    // Emit score update with audit data
    emit('score-updated', {
      studentId: student.id,
      score: student.score,
      examNumber: student.examNumber,
      auditData: auditData
    })

    sweetAlert.success(
      'Score Validated & Saved',
      'The score has been validated according to MANEB SOPs and saved successfully.'
    )
  }

  closeValidationModal()
}

const openNotesModal = (student: Student) => {
  selectedStudentForNotes.value = student
  currentNotes.value = student.notes
  isNotesModalOpen.value = true
}

const closeNotesModal = () => {
  isNotesModalOpen.value = false
  selectedStudentForNotes.value = null
  currentNotes.value = ''
}

const saveNotes = async () => {
  if (!selectedStudentForNotes.value) return

  isSavingNotes.value = true

  try {
    // Mock API call to save notes
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Update the student's notes
    selectedStudentForNotes.value.notes = currentNotes.value
    selectedStudentForNotes.value.hasNotes = currentNotes.value.trim() !== ''

    sweetAlert.success('Notes Saved', 'Student notes have been saved successfully.')
    closeNotesModal()

  } catch (error) {
    console.error('Error saving notes:', error)
    sweetAlert.error('Error', 'Failed to save notes. Please try again.')
  } finally {
    isSavingNotes.value = false
  }
}

// Helper methods for filter names
const getDivisionName = () => props.selectedFilters?.divisionName || 'Unknown Division'
const getDistrictName = () => props.selectedFilters?.districtName || 'Unknown District'
const getCenterName = () => props.selectedFilters?.centerName || 'Unknown Center'
const getExamTypeName = () => props.selectedFilters?.examTypeName || 'Unknown Exam Type'
const getSchoolName = () => props.selectedFilters?.schoolName || 'Unknown School'
const getSubjectName = () => props.selectedFilters?.subjectName || 'Unknown Subject'
const getPaperName = () => props.selectedFilters?.paperName || 'Unknown Paper'
const getScoreTypeName = () => props.selectedFilters?.scoreTypeName || 'Unknown Score Type'

onMounted(() => {
  loadAvailableExamNumbers()
})

onUnmounted(() => {
  // Cleanup search timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value)
  }
})
</script>

<style scoped>
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}

.focus\:ring-maneb-primary\/50:focus {
  --tw-ring-color: rgba(161, 44, 44, 0.5);
}

.bg-maneb-primary-dark {
  background-color: #8b2424;
}

/* Searchable dropdown styles */
.searchable-dropdown {
  position: relative;
}

.dropdown-list {
  max-height: 240px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #a12c2c #f1f1f1;
}

.dropdown-list::-webkit-scrollbar {
  width: 6px;
}

.dropdown-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.dropdown-list::-webkit-scrollbar-thumb {
  background: #a12c2c;
  border-radius: 3px;
}

.dropdown-list::-webkit-scrollbar-thumb:hover {
  background: #8b2424;
}

.dropdown-item-highlighted {
  background-color: #a12c2c !important;
  color: white !important;
}

.dropdown-item-highlighted .text-gray-500 {
  color: rgba(255, 255, 255, 0.8) !important;
}
</style>
